```bash
Benchmarking summary:
+-----------------------------------+-----------+
| Key                               |     Value |
+===================================+===========+
| Time taken for tests (s)          | 2439.47   |
+-----------------------------------+-----------+
| Number of concurrency             |  150      |
+-----------------------------------+-----------+
| Total requests                    |  300      |
+-----------------------------------+-----------+
| Succeed requests                  |  300      |
+-----------------------------------+-----------+
| Failed requests                   |    0      |
+-----------------------------------+-----------+
| Output token throughput (tok/s)   |  107.763  |
+-----------------------------------+-----------+
| Total token throughput (tok/s)    |  110.959  |
+-----------------------------------+-----------+
| Request throughput (req/s)        |    0.123  |
+-----------------------------------+-----------+
| Average latency (s)               | 1004.68   |
+-----------------------------------+-----------+
| Average time to first token (s)   |    3.8808 |
+-----------------------------------+-----------+
| Average time per output token (s) |    1.1611 |
+-----------------------------------+-----------+
| Average input tokens per request  |   25.9867 |
+-----------------------------------+-----------+
| Average output tokens per request |  876.28   |
+-----------------------------------+-----------+
| Average package latency (s)       |    1.1421 |
+-----------------------------------+-----------+
| Average package per request       |  876.277  |
+-----------------------------------+-----------+
2025-07-18 11:25:55,842 - evalscope - INFO - 
Percentile results:
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
| Percentiles | TTFT (s) | ITL (s) | TPOT (s) | Latency (s) | Input tokens | Output tokens | Output (tok/s) | Total (tok/s) |
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
|     10%     |  2.3947  | 0.8337  |  1.0103  |  607.7232   |      17      |      508      |     0.8044     |    0.8271     |
|     25%     |  2.4156  | 1.1498  |  1.1377  |  773.1512   |      20      |      659      |     0.8125     |    0.8367     |
|     50%     |  4.0419  | 1.1963  |  1.2012  |  968.3693   |      25      |      836      |     0.8251     |    0.8572     |
|     66%     |  4.0589  | 1.2059  |  1.2185  |  1108.4362  |      29      |      953      |     0.829      |    0.8755     |
|     75%     |  4.3507  | 1.2113  |  1.2257  |  1183.501   |      31      |     1030      |     0.8784     |    0.9116     |
|     80%     |  6.2215  | 1.2156  |  1.2284  |  1262.3445  |      33      |     1102      |     0.9042     |    0.9307     |
|     90%     |  6.231   | 1.2309  |   1.24   |  1410.7212  |      36      |     1268      |     0.9934     |    1.0188     |
|     95%     |  6.2366  | 1.2817  |  1.2529  |  1678.6607  |      38      |     1507      |     1.1327     |    1.1577     |
|     98%     |  6.2395  | 1.9499  |  1.2722  |  2236.057   |      40      |     2048      |     1.3395     |    1.3553     |
|     99%     |  6.2405  | 2.4037  |  1.2755  |  2236.0816  |      45      |     2048      |     1.4904     |    1.5154     |
```

```bash
benchmarking summary:
+-----------------------------------+-----------+
| Key                               |     Value |
+===================================+===========+
| Time taken for tests (s)          | 2590.84   |
+-----------------------------------+-----------+
| Number of concurrency             |  100      |
+-----------------------------------+-----------+
| Total requests                    |  300      |
+-----------------------------------+-----------+
| Succeed requests                  |  300      |
+-----------------------------------+-----------+
| Failed requests                   |    0      |
+-----------------------------------+-----------+
| Output token throughput (tok/s)   |  100.976  |
+-----------------------------------+-----------+
| Total token throughput (tok/s)    |  103.986  |
+-----------------------------------+-----------+
| Request throughput (req/s)        |    0.1158 |
+-----------------------------------+-----------+
| Average latency (s)               |  753.354  |
+-----------------------------------+-----------+
| Average time to first token (s)   |    2.6233 |
+-----------------------------------+-----------+
| Average time per output token (s) |    0.869  |
+-----------------------------------+-----------+
| Average input tokens per request  |   25.9867 |
+-----------------------------------+-----------+
| Average output tokens per request |  872.047  |
+-----------------------------------+-----------+
| Average package latency (s)       |    0.8609 |
+-----------------------------------+-----------+
| Average package per request       |  872.027  |
+-----------------------------------+-----------+
2025-07-18 10:15:22,294 - evalscope - INFO - 
Percentile results:
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
| Percentiles | TTFT (s) | ITL (s) | TPOT (s) | Latency (s) | Input tokens | Output tokens | Output (tok/s) | Total (tok/s) |
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
|     10%     |  1.7473  | 0.7099  |  0.7621  |  437.9992   |      17      |      491      |     1.0951     |    1.1199     |
|     25%     |  1.7759  | 0.8583  |  0.887   |  583.2088   |      20      |      660      |     1.0979     |    1.1283     |
|     50%     |  1.8509  | 0.8818  |  0.9049  |  718.6413   |      25      |      844      |     1.1002     |    1.1455     |
|     66%     |  2.2882  | 0.9031  |  0.9066  |  838.2778   |      29      |      949      |     1.1038     |    1.1607     |
|     75%     |  3.9384  | 0.9215  |  0.9071  |  892.8544   |      31      |     1018      |     1.1244     |    1.1869     |
|     80%     |  3.9422  | 0.9428  |  0.9077  |  935.6885   |      33      |     1102      |     1.1754     |    1.2245     |
|     90%     |  3.9555  | 0.9875  |  0.9102  |  1077.6852  |      36      |     1266      |     1.3191     |    1.3696     |
|     95%     |  5.0458  | 1.0035  |  0.9111  |  1239.7529  |      38      |     1507      |     1.4605     |    1.5009     |
|     98%     |  5.0495  | 1.0511  |  0.9117  |  1489.3023  |      40      |     1801      |     1.7456     |    1.7822     |
|     99%     |  5.0507  |  1.14   |  0.912   |  1861.1708  |      45      |     2048      |     1.9213     |    1.9674     |
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
```

``` bash 
```


