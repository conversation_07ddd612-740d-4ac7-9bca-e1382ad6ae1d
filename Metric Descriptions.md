Metric Descriptions
Metrics

Metric

Explanation

Formula

Time taken for tests

Total time from the start to the end of the test process

Last request end time - First request start time

Number of concurrency

Number of clients sending requests simultaneously

Preset value

Total requests

Total number of requests sent during the testing process

Successful requests + Failed requests

Succeed requests

Number of requests completed successfully and returning expected results

Direct count

Failed requests

Number of requests that failed to complete successfully

Direct count

Output token throughput (tok/s)

Average number of tokens processed per second

Total output tokens / Time taken for tests

Total token throughput (tok/s)

Average number of tokens processed per second

Total tokens / Time taken for tests

Request throughput (req/s)

Average number of successful queries processed per second

Successful requests / Time taken for tests

Total latency

Sum of latency times for all successful requests

Sum of all successful request latencies

Average latency

Average time from sending a request to receiving a complete response

Total latency / Successful requests

Average time to first token

Average time from sending a request to receiving the first response token

Total first chunk latency / Successful requests

Average time per output token

Average time required to generate each output token (exclude first token)

Total time per output token / Successful requests

Average input tokens per request

Average number of input tokens per request

Total input tokens / Successful requests

Average output tokens per request

Average number of output tokens per request

Total output tokens / Successful requests

Average package latency

Average delay time for receiving each data package

Total package time / Total packages

Average package per request

Average number of data packages received per request

Total packages / Successful requests

Percentile Metrics

Metric

Explanation

Time to First Token

The time from sending a request to generating the first token (in seconds), assessing the initial packet delay.

Inter-token Latency

The time interval between generating each output token (in seconds), assessing the smoothness of output.

Time per Output Token

The time required to generate each output token (excluding the first token, in seconds), assessing decoding speed.

Latency

The time from sending a request to receiving a complete response (in seconds): Time to First Token + Time per Output Token * Output tokens.

Input Tokens

The number of tokens input in the request.

Output Tokens

The number of tokens generated in the response.

Output Throughput

The number of tokens output per second: Output tokens / Latency.

Total Throughput

The number of tokens processed per second: (Input tokens + Output tokens) / Latency.