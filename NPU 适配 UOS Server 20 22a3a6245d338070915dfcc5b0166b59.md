# NPU  适配 UOS Server 20

```bash
[root@node01 ~]# lscpu
Architecture:                       aarch64
CPU op-mode(s):                     64-bit
Byte Order:                         Little Endian
CPU(s):                             96
On-line CPU(s) list:                0-95
Thread(s) per core:                 1
Core(s) per socket:                 48
Socket(s):                          2
NUMA node(s):                       1
Vendor ID:                          HiSilicon
BIOS Vendor ID:                     HiSilicon
Model:                              0
Model name:                         Kunpeng-920
BIOS Model name:                    HUAWEI Kunpeng 920 5250
Stepping:                           0x1
CPU max MHz:                        2600.0000
CPU min MHz:                        200.0000
BogoMIPS:                           200.00
L1d cache:                          6 MiB
L1i cache:                          6 MiB
L2 cache:                           48 MiB
L3 cache:                           96 MiB
NUMA node0 CPU(s):                  0-95
Vulnerability Gather data sampling: Not affected
Vulnerability Itlb multihit:        Not affected
Vulnerability L1tf:                 Not affected
Vulnerability Mds:                  Not affected
Vulnerability Meltdown:             Not affected
Vulnerability Mmio stale data:      Not affected
Vulnerability Retbleed:             Not affected
Vulnerability Spec store bypass:    Vulnerable
Vulnerability Spectre v1:           Mitigation; __user pointer sanitization
Vulnerability Spectre v2:           Not affected
Vulnerability Srbds:                Not affected
Vulnerability Tsx async abort:      Not affected
Flags:                              fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm jscvt fcma dcpop asimddp asimdfhm
```

```bash

[root@node01 ~]# lspci
00:00.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
00:04.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
00:08.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
00:0c.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
00:10.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
00:11.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
00:12.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
01:00.0 Processing accelerators: Huawei Technologies Co., Ltd. Device d500 (rev 23)
02:00.0 Processing accelerators: Huawei Technologies Co., Ltd. Device d500 (rev 23)
04:00.0 RAID bus controller: Broadcom / LSI MegaRAID Tri-Mode SAS3408 (rev 01)
05:00.0 Signal processing controller: Huawei Technologies Co., Ltd. iBMA Virtual Network Adapter (rev 01)
06:00.0 VGA compatible controller: Huawei Technologies Co., Ltd. Hi171x Series [iBMC Intelligent Management system chip w/VGA support] (rev 01)
74:01.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCI-PCI Bridge (rev 20)
74:02.0 Serial Attached SCSI controller: Huawei Technologies Co., Ltd. HiSilicon SAS 3.0 HBA (rev 21)
74:03.0 SATA controller: Huawei Technologies Co., Ltd. HiSilicon AHCI HBA (rev 21)
74:04.0 Serial Attached SCSI controller: Huawei Technologies Co., Ltd. HiSilicon SAS 3.0 HBA (rev 21)
7a:00.0 USB controller: Huawei Technologies Co., Ltd. HiSilicon USB 1.1 Host Controller (rev 21)
7a:01.0 USB controller: Huawei Technologies Co., Ltd. HiSilicon USB 2.0 2-port Host Controller (rev 21)
7a:02.0 USB controller: Huawei Technologies Co., Ltd. HiSilicon USB 3.0 Host Controller (rev 21)
7b:00.0 System peripheral: Huawei Technologies Co., Ltd. HiSilicon Embedded DMA Engine (rev 21)
7c:00.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCI-PCI Bridge (rev 20)
80:00.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
80:04.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
80:08.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
80:0a.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
80:0c.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
80:0e.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
80:10.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCIe Root Port with Gen4 (rev 21)
81:00.0 Processing accelerators: Huawei Technologies Co., Ltd. Device d500 (rev 23)
82:00.0 Processing accelerators: Huawei Technologies Co., Ltd. Device d500 (rev 23)
83:00.0 Non-Volatile memory controller: Samsung Electronics Co Ltd NVMe SSD Controller PM9A1/PM9A3/980PRO
87:00.0 Ethernet controller: Mellanox Technologies MT27710 Family [ConnectX-4 Lx]
87:00.1 Ethernet controller: Mellanox Technologies MT27710 Family [ConnectX-4 Lx]
b4:01.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCI-PCI Bridge (rev 20)
b4:02.0 Serial Attached SCSI controller: Huawei Technologies Co., Ltd. HiSilicon SAS 3.0 HBA (rev 21)
b4:03.0 SATA controller: Huawei Technologies Co., Ltd. HiSilicon AHCI HBA (rev 21)
b4:04.0 Serial Attached SCSI controller: Huawei Technologies Co., Ltd. HiSilicon SAS 3.0 HBA (rev 21)
ba:00.0 USB controller: Huawei Technologies Co., Ltd. HiSilicon USB 1.1 Host Controller (rev 21)
ba:01.0 USB controller: Huawei Technologies Co., Ltd. HiSilicon USB 2.0 2-port Host Controller (rev 21)
ba:02.0 USB controller: Huawei Technologies Co., Ltd. HiSilicon USB 3.0 Host Controller (rev 21)
bb:00.0 System peripheral: Huawei Technologies Co., Ltd. HiSilicon Embedded DMA Engine (rev 21)
bc:00.0 PCI bridge: Huawei Technologies Co., Ltd. HiSilicon PCI-PCI Bridge (rev 20)
```

```bash

docker tag swr.cn-south-1.myhuaweicloud.com/ascendhub/npu-exporter:v6.0.0 npu-exporter:v6.0.0

docker tag swr.cn-south-1.myhuaweicloud.com/ascendhub/ascend-k8sdeviceplugin:v6.0.0 ascend-k8sdeviceplugin:v6.0.0

hub.cetccloud.io:5000/jdcloud/ascend-k8sdeviceplugin:v6.0.0

```

## 软件包

[https://gitee.com/ascend/ascend-device-plugin](https://gitee.com/ascend/ascend-device-plugin)

```bash

https://gitee.com/ascend/mind-cluster/releases/tag/v6.0.0

## x86
 Ascend-docker-runtime_6.0.0_linux-x86_64.run
 Ascend-mindxdl-device-plugin_6.0.0_linux-x86_64.zip
 Ascend-mindxdl-npu-exporter_6.0.0_linux-x86_64.zip
 
 ## arm64
 Ascend-mindxdl-npu-exporter_6.0.0_linux-aarch64.zip
 Ascend-mindxdl-device-plugin_6.0.0_linux-aarch64.zip
 Ascend-docker-runtime_6.0.0_linux-aarch64.run
```

## 创建用户

```bash
# groupadd usergroup
# useradd -g usergroup -d /home/<USER>/bin/bash

groupadd HwHiAiUser
useradd -g HwHiAiUser -d /home/<USER>/bin/bash
```

## 创建日志目录

```bash
 
mkdir -m 755 /var/log/mindx-dl
chown root:root /var/log/mindx-dl 
mkdir -m 750 /var/log/mindx-dl/devicePlugin
chown root:root /var/log/mindx-dl/devicePlugin
```

## **创建节点标签**

```bash
kubectl label nodes node01 masterselector=dls-master-node
kubectl label nodes node01 node-role.kubernetes.io/worker=worker
kubectl label nodes node01 workerselector=dls-worker-node
kubectl label nodes node01 host-arch=huawei-arm
## 具体要对应卡类型 如Ascend910P
kubectl label nodes node01 accelerator=huawei-Ascend910P (huawei-Ascend910、huawei-Ascend910P、huawei-Ascend310P、huawei-Ascend310)

```

## 驱动安装 **Atlas 中心推理卡 24.1.0 NPU驱动和固件安装指南 03**

[support.huawei.com](https://support.huawei.com/enterprise/zh/doc/EDOC1100441350/41efe8e3?idPath=23710424%7C251366513%7C254884019%7C261408772%7C252764743)

```yaml
(base) [root@node01 ~]# npu-smi info 
+--------------------------------------------------------------------------------------------------------+
| npu-smi ********                                 Version: ********                                     |
+-------------------------------+-----------------+------------------------------------------------------+
| NPU     Name                  | Health          | Power(W)     Temp(C)           Hugepages-Usage(page) |
| Chip    Device                | Bus-Id          | AICore(%)    Memory-Usage(MB)                        |
+===============================+=================+======================================================+
| 1       310P3                 | OK              | NA           71                9501  / 9501          |
| 0       0                     | 0000:01:00.0    | 100          20988/ 21544                            |
+-------------------------------+-----------------+------------------------------------------------------+
| 1       310P3                 | OK              | NA           69                9955  / 9955          |
| 1       1                     | 0000:01:00.0    | 86           21053/ 21053                            |
+===============================+=================+======================================================+
| 2       310P3                 | OK              | NA           70                9995  / 9995          |
| 0       2                     | 0000:02:00.0    | 1            21544/ 21544                            |
+-------------------------------+-----------------+------------------------------------------------------+
| 2       310P3                 | OK              | NA           69                8902  / 8902          |
| 1       3                     | 0000:02:00.0    | 3            20234/ 21053                            |
+===============================+=================+======================================================+
| 4       310P3                 | OK              | NA           73                9501  / 9501          |
| 0       4                     | 0000:81:00.0    | 25           20974/ 21544                            |
+-------------------------------+-----------------+------------------------------------------------------+
| 4       310P3                 | OK              | NA           72                9955  / 9955          |
| 1       5                     | 0000:81:00.0    | 35           21053/ 21053                            |
+===============================+=================+======================================================+
| 5       310P3                 | OK              | NA           68                10150 / 10150         |
| 0       6                     | 0000:82:00.0    | 95           21544/ 21544                            |
+-------------------------------+-----------------+------------------------------------------------------+
| 5       310P3                 | OK              | NA           67                9306  / 9306          |
| 1       7                     | 0000:82:00.0    | 100          20592/ 21053                            |
+===============================+=================+======================================================+
+-------------------------------+-----------------+------------------------------------------------------+
| NPU     Chip                  | Process id      | Process name             | Process memory(MB)        |
+===============================+=================+======================================================+
| 1       0                     | 705010          | python3                  | 19441                     |
| 1       1                     | 705011          | python3                  | 20253                     |
+===============================+=================+======================================================+
| 2       0                     | 705012          | python3                  | 20165                     |
| 2       1                     | 705013          | python3                  | 18891                     |
+===============================+=================+======================================================+
| 4       0                     | 705014          | python3                  | 19441                     |
| 4       1                     | 705015          | python3                  | 20252                     |
+===============================+=================+======================================================+
| 5       0                     | 705016          | python3                  | 20630                     |
| 5       1                     | 705017          | python3                  | 19062                     |
+===============================+=================+======================================================+
```

## Ascend-docker-runtime**安装**

```bash
#加权限
chmod u+x Ascend-docker-runtime_6.0.0_linux-aarch64.run
# check文件
./Ascend-docker-runtime_6.0.0_linux-aarch64.run --check
# 安装
./Ascend-docker-runtime_6.0.0_linux-aarch64.run --install

cd /var/lib/rancher/k3s/agent/etc/containerd/
cp -ax config.toml config.toml.tmpl
```

### 编辑config.toml.tmpl，修改如下：

```bash
[plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]
  SystemdCgroup = true
  BinaryName = "/usr/local/Ascend/Ascend-Docker-Runtime/ascend-docker-runtime"
```

## 重启k3s

```bash
systemctl daemon-reload && systemctl restart k3s
```

## Ascend-mindxdl-device-plugin 安装

[mind-cluster Releases - Gitee](https://gitee.com/ascend/mind-cluster/releases)

[Ascend Docker Runtime-手动安装-安装部署-组件安装-MindX DL6.0.0开发文档-昇腾社区](https://www.hiascend.com/document/detail/zh/mindx-dl/600/clusterscheduling/clusterschedulingig/clusterschedulingig/dlug_installation_017.html)

[gitee.com](https://gitee.com/ascend/ascend-device-plugin)

```bash

unzip Ascend-mindxdl-device-plugin_6.0.0_linux-aarch64.zip -d Ascend-mindxdl-device-plugin_6.0.0

kubectl apply -f device-plugin-310P-v6.0.0.yaml 
```

### 推理服务测试

```bash

5.10.0-74.3.uel20

4.19.90-2403.3.0.0270.90.uel20
```

```yaml

# InferenceService - DeepSeek 70B 推理服务
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  annotations:
    nginx.ingress.kubernetes.io/enable-cors: "true"
    serving.kserve.io/enable-nodeport: "true"
    serving.kserve.io/autoscalerClass: none
  name: deepseek-70b
spec:
  predictor:
    minReplicas: 1
    model:
      args:
      - --served-model-name=deepseek-32b
      - --tensor-parallel-size=8
      - --port=8080
      - --enforce-eager
      - --dtype=float16
      modelFormat:
        name: vllm-ascend
      storageUri: pvc://model-pvc/DeepSeek-R1-Distill-Qwen-32B

      ports:
      - containerPort: 8080
        protocol: TCP
      env:
      - name: PYTORCH_NPU_ALLOC_CONF
        value: max_split_size_mb:256
      resources:
        limits:
          cpu: "64"
          memory: 256Gi
          huawei.com/Ascend310P: "8"
        requests:
          cpu: "64"
          memory: 256Gi
          huawei.com/Ascend310P: "8"
      volumeMounts:
      - mountPath: /dev/shm
        name: dshm
    volumes:
    - name: dshm
"isvc.yaml" 48L, 1132B                                                                                           29,7          Top
```

![image.png](NPU%20%E9%80%82%E9%85%8D%20UOS%20Server%2020%2022a3a6245d338070915dfcc5b0166b59/image.png)

![image.png](NPU%20%E9%80%82%E9%85%8D%20UOS%20Server%2020%2022a3a6245d338070915dfcc5b0166b59/image%201.png)

```yaml
evalscope perf \
  --parallel  50 500  \
  --number  100 1000  \
  --model deepseek-32b \
  --url  http://192.168.196.148:8080/v1/v1/chat/completions  \
  --api openai \
  --dataset random \
  --max-tokens 1024 \
  --min-tokens 1024 \
  --prefix-length 0 \
  --min-prompt-length 1024 \
  --max-prompt-length 1024 \
  --tokenizer-path /home/<USER>/DeepSeek-R1-Distill-Qwen-32B \
  --extra-args '{"ignore_eos": true}'
```

```yaml
evalscope perf \
 --url http://192.168.196.147:8080/v1/chat/completions \
 --parallel  200 \
 --number  400  \
 --model deepseek-32b  \
 --api openai \
 --temperature 0.9
 --dataset openqa \
 --tokenizer-path /home/<USER>/DeepSeek-R1-Distill-Qwen-32B

```

## 速度基准测试

```yaml

evalscope perf \
 --parallel 1 \
 --url http://192.168.196.131:8080/v1/completions \
 --model deepseek-32b \
 --log-every-n-query 5 \
 --connect-timeout 6000 \
 --read-timeout 6000 \
 --number  200  \
 --api openai \
 --dataset speed_benchmark \

```

### parallel_5_number_10

```yaml
### benchmark_summary.json
{
    "Time taken for tests (s)": 328.7183,
    "Number of concurrency": 5,
    "Total requests": 10,
    "Succeed requests": 10,
    "Failed requests": 0,
    "Output token throughput (tok/s)": 31.1513,
    "Total token throughput (tok/s)": 31.5468,
    "Request throughput (req/s)": 0.0304,
    "Average latency (s)": 164.3422,
    "Average time to first token (s)": 0.6155,
    "Average time per output token (s)": 0.1603,
    "Average input tokens per request": 13.0,
    "Average output tokens per request": 1024.0,
    "Average package latency (s)": 0.1603,
    "Average package per request": 1021.5
}
```

 parallel_50_number_200

```yaml
### benchmark_percentile.json
[
    {
        "Percentiles": "10%",
        "TTFT (s)": 1.0746,
        "ITL (s)": 0.4788,
        "TPOT (s)": 0.502,
        "Latency (s)": 261.8905,
        "Input tokens": 17,
        "Output tokens": 475,
        "Output (tok/s)": 1.79,
        "Total (tok/s)": 1.8469
    },
    {
        "Percentiles": "25%",
        "TTFT (s)": 1.093,
        "ITL (s)": 0.5323,
        "TPOT (s)": 0.5445,
        "Latency (s)": 370.0153,
        "Input tokens": 21,
        "Output tokens": 704,
        "Output (tok/s)": 1.8077,
        "Total (tok/s)": 1.8591
    },
    {
        "Percentiles": "50%",
        "TTFT (s)": 1.1271,
        "ITL (s)": 0.5465,
        "TPOT (s)": 0.5481,
        "Latency (s)": 463.9898,
        "Input tokens": 26,
        "Output tokens": 888,
        "Output (tok/s)": 1.8125,
        "Total (tok/s)": 1.8792
    },
    {
        "Percentiles": "66%",
        "TTFT (s)": 1.1578,
        "ITL (s)": 0.5545,
        "TPOT (s)": 0.5494,
        "Latency (s)": 517.1119,
        "Input tokens": 29,
        "Output tokens": 965,
        "Output (tok/s)": 1.8277,
        "Total (tok/s)": 1.8972
    },
    {
        "Percentiles": "75%",
        "TTFT (s)": 2.0271,
        "ITL (s)": 0.5603,
        "TPOT (s)": 0.55,
        "Latency (s)": 555.7754,
        "Input tokens": 31,
        "Output tokens": 1024,
        "Output (tok/s)": 1.831,
        "Total (tok/s)": 1.9269
    },
    {
        "Percentiles": "80%",
        "TTFT (s)": 3.2432,
        "ITL (s)": 0.5661,
        "TPOT (s)": 0.5515,
        "Latency (s)": 560.2086,
        "Input tokens": 33,
        "Output tokens": 1024,
        "Output (tok/s)": 1.834,
        "Total (tok/s)": 1.9533
    },
    {
        "Percentiles": "90%",
        "TTFT (s)": 3.252,
        "ITL (s)": 0.5813,
        "TPOT (s)": 0.5568,
        "Latency (s)": 565.8115,
        "Input tokens": 37,
        "Output tokens": 1024,
        "Output (tok/s)": 2.0026,
        "Total (tok/s)": 2.107
    },
    {
        "Percentiles": "95%",
        "TTFT (s)": 3.2553,
        "ITL (s)": 0.6005,
        "TPOT (s)": 0.5585,
        "Latency (s)": 565.8193,
        "Input tokens": 39,
        "Output tokens": 1024,
        "Output (tok/s)": 2.181,
        "Total (tok/s)": 2.286
    },
    {
        "Percentiles": "98%",
        "TTFT (s)": 3.2568,
        "ITL (s)": 0.63,
        "TPOT (s)": 0.5593,
        "Latency (s)": 569.5739,
        "Input tokens": 42,
        "Output tokens": 1024,
        "Output (tok/s)": 2.4544,
        "Total (tok/s)": 2.5047
    },
    {
        "Percentiles": "99%",
        "TTFT (s)": 3.2578,
        "ITL (s)": 0.6596,
        "TPOT (s)": 0.5596,
        "Latency (s)": 571.8963,
        "Input tokens": 45,
        "Output tokens": 1024,
        "Output (tok/s)": 2.5923,
        "Total (tok/s)": 2.6815
    }
](
```

```yaml

### benchmark_summary.json  

{
    "Time taken for tests (s)": 1884.1823,
    "Number of concurrency": 50,
    "Total requests": 200,
    "Succeed requests": 200,
    "Failed requests": 0,
    "Output token throughput (tok/s)": 86.5543,
    "Total token throughput (tok/s)": 89.3507,
    "Request throughput (req/s)": 0.1061,
    "Average latency (s)": 438.5153,
    "Average time to first token (s)": 1.6693,
    "Average time per output token (s)": 0.5371,
    "Average input tokens per request": 26.345,
    "Average output tokens per request": 815.42,
    "Average package latency (s)": 0.5357,
    "Average package per request": 815.42
}
```

![image.png](NPU%20%E9%80%82%E9%85%8D%20UOS%20Server%2020%2022a3a6245d338070915dfcc5b0166b59/image%202.png)

 parallel_100_number_300

```yaml

{
    "Time taken for tests (s)": 2590.842,
    "Number of concurrency": 100,
    "Total requests": 300,
    "Succeed requests": 300,
    "Failed requests": 0,
    "Output token throughput (tok/s)": 100.9764,
    "Total token throughput (tok/s)": 103.9855,
    "Request throughput (req/s)": 0.1158,
    "Average latency (s)": 753.354,
    "Average time to first token (s)": 2.6233,
    "Average time per output token (s)": 0.869,
    "Average input tokens per request": 25.9867,
    "Average output tokens per request": 872.0467,
    "Average package latency (s)": 0.8609,
    "Average package per request": 872.0267
}
```

```yaml

[
    {
        "Percentiles": "10%",
        "TTFT (s)": 1.7473,
        "ITL (s)": 0.7099,
        "TPOT (s)": 0.7621,
        "Latency (s)": 437.9992,
        "Input tokens": 17,
        "Output tokens": 491,
        "Output (tok/s)": 1.0951,
        "Total (tok/s)": 1.1199
    },
    {
        "Percentiles": "25%",
        "TTFT (s)": 1.7759,
        "ITL (s)": 0.8583,
        "TPOT (s)": 0.887,
        "Latency (s)": 583.2088,
        "Input tokens": 20,
        "Output tokens": 660,
        "Output (tok/s)": 1.0979,
        "Total (tok/s)": 1.1283
    },
    {
        "Percentiles": "50%",
        "TTFT (s)": 1.8509,
        "ITL (s)": 0.8818,
        "TPOT (s)": 0.9049,
        "Latency (s)": 718.6413,
        "Input tokens": 25,
        "Output tokens": 844,
        "Output (tok/s)": 1.1002,
        "Total (tok/s)": 1.1455
    },
    {
        "Percentiles": "66%",
        "TTFT (s)": 2.2882,
        "ITL (s)": 0.9031,
        "TPOT (s)": 0.9066,
        "Latency (s)": 838.2778,
        "Input tokens": 29,
        "Output tokens": 949,
        "Output (tok/s)": 1.1038,
        "Total (tok/s)": 1.1607
    },
    {
        "Percentiles": "75%",
        "TTFT (s)": 3.9384,
        "ITL (s)": 0.9215,
        "TPOT (s)": 0.9071,
        "Latency (s)": 892.8544,
        "Input tokens": 31,
        "Output tokens": 1018,
        "Output (tok/s)": 1.1244,
        "Total (tok/s)": 1.1869
    },
    {
        "Percentiles": "80%",
        "TTFT (s)": 3.9422,
        "ITL (s)": 0.9428,
        "TPOT (s)": 0.9077,
        "Latency (s)": 935.6885,
        "Input tokens": 33,
        "Output tokens": 1102,
        "Output (tok/s)": 1.1754,
        "Total (tok/s)": 1.2245
    },
    {
        "Percentiles": "90%",
        "TTFT (s)": 3.9555,
        "ITL (s)": 0.9875,
        "TPOT (s)": 0.9102,
        "Latency (s)": 1077.6852,
        "Input tokens": 36,
        "Output tokens": 1266,
        "Output (tok/s)": 1.3191,
        "Total (tok/s)": 1.3696
    },
    {
        "Percentiles": "95%",
        "TTFT (s)": 5.0458,
        "ITL (s)": 1.0035,
        "TPOT (s)": 0.9111,
        "Latency (s)": 1239.7529,
        "Input tokens": 38,
        "Output tokens": 1507,
        "Output (tok/s)": 1.4605,
        "Total (tok/s)": 1.5009
    },
    {
        "Percentiles": "98%",
        "TTFT (s)": 5.0495,
        "ITL (s)": 1.0511,
        "TPOT (s)": 0.9117,
        "Latency (s)": 1489.3023,
        "Input tokens": 40,
        "Output tokens": 1801,
        "Output (tok/s)": 1.7456,
        "Total (tok/s)": 1.7822
    },
    {
        "Percentiles": "99%",
        "TTFT (s)": 5.0507,
        "ITL (s)": 1.14,
        "TPOT (s)": 0.912,
        "Latency (s)": 1861.1708,
        "Input tokens": 45,
        "Output tokens": 2048,
        "Output (tok/s)": 1.9213,
        "Total (tok/s)": 1.9674
    }
]
```

### parallel_150_number_300

```yaml
## benchmark_percentile.json
[
    {
        "Percentiles": "10%",
        "TTFT (s)": 2.3947,
        "ITL (s)": 0.8337,
        "TPOT (s)": 1.0103,
        "Latency (s)": 607.7232,
        "Input tokens": 17,
        "Output tokens": 508,
        "Output (tok/s)": 0.8044,
        "Total (tok/s)": 0.8271
    },
    {
        "Percentiles": "25%",
        "TTFT (s)": 2.4156,
        "ITL (s)": 1.1498,
        "TPOT (s)": 1.1377,
        "Latency (s)": 773.1512,
        "Input tokens": 20,
        "Output tokens": 659,
        "Output (tok/s)": 0.8125,
        "Total (tok/s)": 0.8367
    },
    {
        "Percentiles": "50%",
        "TTFT (s)": 4.0419,
        "ITL (s)": 1.1963,
        "TPOT (s)": 1.2012,
        "Latency (s)": 968.3693,
        "Input tokens": 25,
        "Output tokens": 836,
        "Output (tok/s)": 0.8251,
        "Total (tok/s)": 0.8572
    },
    {
        "Percentiles": "66%",
        "TTFT (s)": 4.0589,
        "ITL (s)": 1.2059,
        "TPOT (s)": 1.2185,
        "Latency (s)": 1108.4362,
        "Input tokens": 29,
        "Output tokens": 953,
        "Output (tok/s)": 0.829,
        "Total (tok/s)": 0.8755
    },
    {
        "Percentiles": "75%",
        "TTFT (s)": 4.3507,
        "ITL (s)": 1.2113,
        "TPOT (s)": 1.2257,
        "Latency (s)": 1183.501,
        "Input tokens": 31,
        "Output tokens": 1030,
        "Output (tok/s)": 0.8784,
        "Total (tok/s)": 0.9116
    },
    {
        "Percentiles": "80%",
        "TTFT (s)": 6.2215,
        "ITL (s)": 1.2156,
        "TPOT (s)": 1.2284,
        "Latency (s)": 1262.3445,
        "Input tokens": 33,
        "Output tokens": 1102,
        "Output (tok/s)": 0.9042,
        "Total (tok/s)": 0.9307
    },
    {
        "Percentiles": "90%",
        "TTFT (s)": 6.231,
        "ITL (s)": 1.2309,
        "TPOT (s)": 1.24,
        "Latency (s)": 1410.7212,
        "Input tokens": 36,
        "Output tokens": 1268,
        "Output (tok/s)": 0.9934,
        "Total (tok/s)": 1.0188
    },
    {
        "Percentiles": "95%",
        "TTFT (s)": 6.2366,
        "ITL (s)": 1.2817,
        "TPOT (s)": 1.2529,
        "Latency (s)": 1678.6607,
        "Input tokens": 38,
        "Output tokens": 1507,
        "Output (tok/s)": 1.1327,
        "Total (tok/s)": 1.1577
    },
    {
        "Percentiles": "98%",
        "TTFT (s)": 6.2395,
        "ITL (s)": 1.9499,
        "TPOT (s)": 1.2722,
        "Latency (s)": 2236.057,
        "Input tokens": 40,
        "Output tokens": 2048,
        "Output (tok/s)": 1.3395,
        "Total (tok/s)": 1.3553
    },
    {
        "Percentiles": "99%",
        "TTFT (s)": 6.2405,
        "ITL (s)": 2.4037,
        "TPOT (s)": 1.2755,
        "Latency (s)": 2236.0816,
        "Input tokens": 45,
        "Output tokens": 2048,
        "Output (tok/s)": 1.4904,
        "Total (tok/s)": 1.5154
    }
]
```

```yaml

### benchmark_summary.json

{
    "Time taken for tests (s)": 2439.4688,
    "Number of concurrency": 150,
    "Total requests": 300,
    "Succeed requests": 300,
    "Failed requests": 0,
    "Output token throughput (tok/s)": 107.7628,
    "Total token throughput (tok/s)": 110.9586,
    "Request throughput (req/s)": 0.123,
    "Average latency (s)": 1004.6801,
    "Average time to first token (s)": 3.8808,
    "Average time per output token (s)": 1.1611,
    "Average input tokens per request": 25.9867,
    "Average output tokens per request": 876.28,
    "Average package latency (s)": 1.1421,
    "Average package per request": 876.2767
}
```

![image.png](NPU%20%E9%80%82%E9%85%8D%20UOS%20Server%2020%2022a3a6245d338070915dfcc5b0166b59/image%203.png)

![image.png](NPU%20%E9%80%82%E9%85%8D%20UOS%20Server%2020%2022a3a6245d338070915dfcc5b0166b59/image%204.png)