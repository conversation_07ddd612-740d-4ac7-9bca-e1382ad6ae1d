# NPU 适配 UOS Server 20 技术报告

## 1. 项目概述

本文档详细记录了在UOS Server 20操作系统上适配华为昇腾NPU的完整过程，包括硬件环境分析、软件安装配置、Kubernetes集群部署和性能测试等关键环节。

### 1.1 技术栈
- **操作系统**: UOS Server 20 (aarch64)
- **硬件平台**: 华为鲲鹏920处理器 + 昇腾310P3 NPU
- **容器编排**: Kubernetes (K3s)
- **AI推理框架**: vLLM + 昇腾适配
- **测试模型**: DeepSeek-R1-Distill-Qwen-32B

### 1.2 项目目标
- 实现NPU在UOS Server 20上的完整适配
- 部署高性能AI推理服务
- 验证系统稳定性和性能表现

## 2. 硬件环境分析

### 2.1 CPU配置
```bash
Architecture:           aarch64
CPU(s):                 96
Model name:             Kunpeng-920
CPU max MHz:            2600.0000
Socket(s):              2
Core(s) per socket:     48
Thread(s) per core:     1
L1d cache:              6 MiB
L1i cache:              6 MiB
L2 cache:               48 MiB
L3 cache:               96 MiB
```

**配置总结:**
- 处理器：华为鲲鹏920，96核心，2.6GHz
- 架构：ARM64 (aarch64)
- 内存缓存：L1d/L1i 6MB，L2 48MB，L3 96MB

### 2.2 NPU设备识别
通过PCI设备扫描识别到的NPU设备：
```bash
01:00.0 Processing accelerators: Huawei Technologies Co., Ltd. Device d500 (rev 23)
02:00.0 Processing accelerators: Huawei Technologies Co., Ltd. Device d500 (rev 23)
81:00.0 Processing accelerators: Huawei Technologies Co., Ltd. Device d500 (rev 23)
82:00.0 Processing accelerators: Huawei Technologies Co., Ltd. Device d500 (rev 23)
```

**设备配置:**
- NPU型号：昇腾310P3
- 设备数量：4个物理卡，每卡2个芯片，共8个逻辑设备
- 总内存：约168GB (每设备21GB)

## 3. 软件环境准备

### 3.1 依赖软件包
从华为昇腾社区下载以下软件包：

**下载地址:**
- 官方仓库：https://gitee.com/ascend/ascend-device-plugin
- 发布版本：https://gitee.com/ascend/mind-cluster/releases/tag/v6.0.0

**aarch64架构软件包:**
```bash
Ascend-docker-runtime_6.0.0_linux-aarch64.run
Ascend-mindxdl-device-plugin_6.0.0_linux-aarch64.zip
Ascend-mindxdl-npu-exporter_6.0.0_linux-aarch64.zip
```

### 3.2 容器镜像准备
```bash
# 拉取并重新标记昇腾相关镜像
docker tag swr.cn-south-1.myhuaweicloud.com/ascendhub/npu-exporter:v6.0.0 npu-exporter:v6.0.0
docker tag swr.cn-south-1.myhuaweicloud.com/ascendhub/ascend-k8sdeviceplugin:v6.0.0 ascend-k8sdeviceplugin:v6.0.0
```

## 4. 系统配置

### 4.1 用户和权限配置
```bash
# 创建昇腾专用用户组和用户
groupadd HwHiAiUser
useradd -g HwHiAiUser -d /home/<USER>/bin/bash

# 创建MindX DL日志目录
mkdir -m 755 /var/log/mindx-dl
chown root:root /var/log/mindx-dl 
mkdir -m 750 /var/log/mindx-dl/devicePlugin
chown root:root /var/log/mindx-dl/devicePlugin
```

### 4.2 Kubernetes节点标签
```bash
# 配置节点标签以支持昇腾NPU调度
kubectl label nodes node01 masterselector=dls-master-node
kubectl label nodes node01 node-role.kubernetes.io/worker=worker
kubectl label nodes node01 workerselector=dls-worker-node
kubectl label nodes node01 host-arch=huawei-arm
kubectl label nodes node01 accelerator=huawei-Ascend310P
```

## 5. NPU驱动安装

### 5.1 驱动安装过程
参考华为官方文档进行驱动安装：
- 文档链接：Atlas 中心推理卡 24.1.0 NPU驱动和固件安装指南

### 5.2 安装验证
使用npu-smi工具验证NPU设备状态：

```bash
npu-smi info
```

**验证结果:**
- 检测到8个NPU设备（4个物理卡，每卡2个芯片）
- 型号：Ascend 310P3
- 所有设备健康状态：OK
- 温度范围：67-73°C
- 内存使用情况：约20GB/21GB

## 6. 容器运行时配置

### 6.1 Ascend Docker Runtime安装
```bash
# 添加执行权限并安装
chmod u+x Ascend-docker-runtime_6.0.0_linux-aarch64.run
./Ascend-docker-runtime_6.0.0_linux-aarch64.run --check
./Ascend-docker-runtime_6.0.0_linux-aarch64.run --install
```

### 6.2 容器运行时配置
```bash
# 备份并修改containerd配置
cd /var/lib/rancher/k3s/agent/etc/containerd/
cp -ax config.toml config.toml.tmpl
```

编辑config.toml.tmpl：
```toml
[plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]
  SystemdCgroup = true
  BinaryName = "/usr/local/Ascend/Ascend-Docker-Runtime/ascend-docker-runtime"
```

```bash
# 重启K3s服务
systemctl daemon-reload && systemctl restart k3s
```

## 7. Device Plugin部署

### 7.1 安装MindX DL Device Plugin
```bash
# 解压并部署Device Plugin
unzip Ascend-mindxdl-device-plugin_6.0.0_linux-aarch64.zip -d Ascend-mindxdl-device-plugin_6.0.0
kubectl apply -f device-plugin-310P-v6.0.0.yaml
```

### 7.2 支持的内核版本
```bash
5.10.0-74.3.uel20
4.19.90-2403.3.0.0270.90.uel20
```

## 8. AI推理服务部署

### 8.1 InferenceService配置
部署DeepSeek 32B推理服务：

```yaml
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  annotations:
    nginx.ingress.kubernetes.io/enable-cors: "true"
    serving.kserve.io/enable-nodeport: "true"
    serving.kserve.io/autoscalerClass: none
  name: deepseek-32b
spec:
  predictor:
    minReplicas: 1
    model:
      args:
      - --served-model-name=deepseek-32b
      - --tensor-parallel-size=8
      - --port=8080
      - --enforce-eager
      - --dtype=float16
      modelFormat:
        name: vllm-ascend
      storageUri: pvc://model-pvc/DeepSeek-R1-Distill-Qwen-32B
      ports:
      - containerPort: 8080
        protocol: TCP
      env:
      - name: PYTORCH_NPU_ALLOC_CONF
        value: max_split_size_mb:256
      resources:
        limits:
          cpu: "64"
          memory: 256Gi
          huawei.com/Ascend310P: "8"
        requests:
          cpu: "64"
          memory: 256Gi
          huawei.com/Ascend310P: "8"
      volumeMounts:
      - mountPath: /dev/shm
        name: dshm
    volumes:
    - name: dshm
        emptyDir:
          medium: Memory
          sizeLimit: 32Gi
```

**配置说明:**
- 使用8个Ascend310P NPU进行张量并行
- 分配64核CPU和256GB内存
- 启用共享内存以提高性能

## 9. 性能测试方案

### 9.1 测试工具
使用evalscope工具进行性能基准测试：

```bash
# 基准速度测试
evalscope perf \
  --parallel 1 \
  --url http://192.168.196.131:8080/v1/completions \
  --model deepseek-32b \
  --log-every-n-query 5 \
  --connect-timeout 6000 \
  --read-timeout 6000 \
  --number 200 \
  --api openai \
  --dataset speed_benchmark
```

### 9.2 测试场景
1. **低并发测试**: parallel=5, requests=10
2. **中等并发测试**: parallel=50, requests=200
3. **高并发测试**: parallel=100, requests=300
4. **极限并发测试**: parallel=150, requests=300

## 10. 性能测试结果

### 10.1 低并发测试结果 (parallel=5, requests=10)

```json
{
    "Time taken for tests (s)": 328.72,
    "Number of concurrency": 5,
    "Total requests": 10,
    "Succeed requests": 10,
    "Failed requests": 0,
    "Output token throughput (tok/s)": 31.15,
    "Total token throughput (tok/s)": 31.55,
    "Request throughput (req/s)": 0.030,
    "Average latency (s)": 164.34,
    "Average time to first token (s)": 0.62,
    "Average time per output token (s)": 0.16,
    "Average input tokens per request": 13.0,
    "Average output tokens per request": 1024.0
}
```

**关键指标分析:**
- 输出token吞吐量：31.15 tok/s
- 平均延迟：164.34秒
- 首token时间：0.62秒
- 成功率：100%

### 10.2 中等并发测试结果 (parallel=50, requests=200)

```json
{
    "Time taken for tests (s)": 1884.18,
    "Number of concurrency": 50,
    "Total requests": 200,
    "Succeed requests": 200,
    "Failed requests": 0,
    "Output token throughput (tok/s)": 86.55,
    "Total token throughput (tok/s)": 89.35,
    "Request throughput (req/s)": 0.106,
    "Average latency (s)": 438.52,
    "Average time to first token (s)": 1.67,
    "Average time per output token (s)": 0.54,
    "Average input tokens per request": 26.35,
    "Average output tokens per request": 815.42
}
```

**关键指标分析:**
- 输出token吞吐量：86.55 tok/s（提升178%）
- 平均延迟：438.52秒
- 首token时间：1.67秒
- 成功率：100%

### 10.3 高并发测试结果 (parallel=100, requests=300)

```json
{
    "Time taken for tests (s)": 2590.84,
    "Number of concurrency": 100,
    "Total requests": 300,
    "Succeed requests": 300,
    "Failed requests": 0,
    "Output token throughput (tok/s)": 100.98,
    "Total token throughput (tok/s)": 103.99,
    "Request throughput (req/s)": 0.116,
    "Average latency (s)": 753.35,
    "Average time to first token (s)": 2.62,
    "Average time per output token (s)": 0.87,
    "Average input tokens per request": 25.99,
    "Average output tokens per request": 872.05
}
```

**关键指标分析:**
- 输出token吞吐量：100.98 tok/s（峰值性能）
- 平均延迟：753.35秒
- 首token时间：2.62秒
- 成功率：100%

### 10.4 极限并发测试结果 (parallel=150, requests=300)

```json
{
    "Time taken for tests (s)": 2439.47,
    "Number of concurrency": 150,
    "Total requests": 300,
    "Succeed requests": 300,
    "Failed requests": 0,
    "Output token throughput (tok/s)": 107.76,
    "Total token throughput (tok/s)": 110.96,
    "Request throughput (req/s)": 0.123,
    "Average latency (s)": 1004.68,
    "Average time to first token (s)": 3.88,
    "Average time per output token (s)": 1.16,
    "Average input tokens per request": 25.99,
    "Average output tokens per request": 876.28
}
```

**关键指标分析:**
- 输出token吞吐量：107.76 tok/s（最高吞吐量）
- 平均延迟：1004.68秒
- 首token时间：3.88秒
- 成功率：100%

## 11. 性能分析总结

### 11.1 吞吐量性能
| 并发数 | 请求数 | 输出吞吐量(tok/s) | 总吞吐量(tok/s) | 性能提升 |
|--------|--------|-------------------|-----------------|----------|
| 5      | 10     | 31.15             | 31.55           | 基准     |
| 50     | 200    | 86.55             | 89.35           | +178%    |
| 100    | 300    | 100.98            | 103.99          | +224%    |
| 150    | 300    | 107.76            | 110.96          | +246%    |

### 11.2 延迟性能
| 并发数 | 平均延迟(s) | 首token时间(s) | 每token时间(s) |
|--------|-------------|----------------|----------------|
| 5      | 164.34      | 0.62           | 0.16           |
| 50     | 438.52      | 1.67           | 0.54           |
| 100    | 753.35      | 2.62           | 0.87           |
| 150    | 1004.68     | 3.88           | 1.16           |

### 11.3 关键发现
1. **最佳性能点**: 并发数150时达到最高吞吐量107.76 tok/s
2. **扩展性**: 从5并发到150并发，吞吐量提升246%
3. **稳定性**: 所有测试场景成功率均为100%
4. **延迟权衡**: 高并发下延迟增加，但整体吞吐量显著提升

## 12. 部署建议

### 12.1 生产环境配置建议
1. **并发设置**: 建议并发数设置在100-150之间以获得最佳吞吐量
2. **资源配置**: 确保充足的CPU和内存资源（64核CPU + 256GB内存）
3. **监控告警**: 部署NPU监控和温度告警机制
4. **负载均衡**: 在多节点环境下配置负载均衡

### 12.2 优化建议
1. **内存优化**: 调整PYTORCH_NPU_ALLOC_CONF参数
2. **网络优化**: 优化网络配置以减少延迟
3. **存储优化**: 使用高性能存储以加快模型加载
4. **缓存策略**: 实施模型缓存策略以提高响应速度

## 13. 总结

本次NPU适配UOS Server 20项目成功实现了以下目标：

1. **完整适配**: 成功在UOS Server 20上部署昇腾310P3 NPU
2. **高性能**: 实现了107.76 tok/s的峰值吞吐量
3. **高稳定性**: 所有测试场景100%成功率
4. **可扩展性**: 验证了从低并发到高并发的良好扩展性

该方案为在国产化操作系统上部署AI推理服务提供了完整的技术路径和性能基准，具有重要的实用价值和参考意义。
